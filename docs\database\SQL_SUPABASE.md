-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.appointments (
  id bigint NOT NULL DEFAULT nextval('appointments_id_seq'::regclass),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id_old bigint,
  channel_type text DEFAULT 'whatsapp'::text,
  channel_identifier text NOT NULL,
  date date NOT NULL,
  time time without time zone NOT NULL,
  type text DEFAULT 'general'::text,
  status text DEFAULT 'scheduled'::text,
  notes text,
  metadata jsonb DEFAULT '{}'::jsonb,
  user_id character varying,
  CONSTRAINT appointments_pkey PRIMARY KEY (id),
  CONSTRAINT fk_appointments_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.authentication_logs (
  id bigint NOT NULL DEFAULT nextval('authentication_logs_id_seq'::regclass),
  created_at timestamp with time zone DEFAULT now(),
  phone text NOT NULL,
  status text NOT NULL,
  timestamp timestamp with time zone DEFAULT now(),
  ip_address text,
  user_agent text,
  user_id_old bigint,
  metadata jsonb DEFAULT '{}'::jsonb,
  user_id character varying,
  CONSTRAINT authentication_logs_pkey PRIMARY KEY (id),
  CONSTRAINT fk_authentication_logs_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.chat_memory_history (
  id bigint NOT NULL DEFAULT nextval('chat_memory_history_id_seq'::regclass),
  session_id bigint NOT NULL,
  message jsonb NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id_old bigint,
  user_id character varying,
  CONSTRAINT chat_memory_history_pkey PRIMARY KEY (id),
  CONSTRAINT fk_cmh_session FOREIGN KEY (session_id) REFERENCES public.chat_sessions(id),
  CONSTRAINT fk_chat_memory_history_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.chat_messages (
  id bigint NOT NULL DEFAULT nextval('chat_messages_id_seq'::regclass),
  session_id bigint NOT NULL,
  user_id_old bigint,
  channel_type text DEFAULT 'whatsapp'::text,
  channel_identifier text,
  message_type text,
  user_message text,
  ai_response text,
  content text NOT NULL,
  tool_used text,
  tool_result text,
  intent_category text,
  message_metadata jsonb DEFAULT '{}'::jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  response_sent boolean DEFAULT false,
  sent_timestamp timestamp with time zone,
  send_status text DEFAULT 'pending'::text,
  whatsapp_response jsonb,
  delivery_status text DEFAULT 'pending'::text,
  delivery_timestamp timestamp with time zone,
  processing_time_ms integer,
  delivery_metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id character varying,
  CONSTRAINT chat_messages_pkey PRIMARY KEY (id),
  CONSTRAINT fk_chat_messages_session FOREIGN KEY (session_id) REFERENCES public.chat_sessions(id),
  CONSTRAINT fk_chat_messages_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.chat_sessions (
  id bigint NOT NULL DEFAULT nextval('chat_sessions_id_seq'::regclass),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id_old bigint,
  channel_type text DEFAULT 'whatsapp'::text,
  channel_identifier text NOT NULL,
  session_data jsonb DEFAULT '{}'::jsonb,
  last_activity timestamp with time zone DEFAULT now(),
  active boolean DEFAULT true,
  user_id character varying,
  CONSTRAINT chat_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT fk_chat_sessions_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.community_assessments (
  id integer NOT NULL DEFAULT nextval('community_assessments_id_seq'::regclass),
  organization_id integer,
  location_id integer,
  conducted_by_cpf character varying,
  assessment_data jsonb DEFAULT '{}'::jsonb,
  ai_analysis jsonb DEFAULT '{}'::jsonb,
  needs_identified jsonb DEFAULT '{}'::jsonb,
  status character varying DEFAULT 'planejado'::character varying CHECK (status::text = ANY (ARRAY['planejado'::character varying, 'em_andamento'::character varying, 'concluido'::character varying, 'revisao'::character varying]::text[])),
  created_at timestamp without time zone DEFAULT now(),
  completed_at timestamp without time zone,
  CONSTRAINT community_assessments_pkey PRIMARY KEY (id),
  CONSTRAINT community_assessments_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id),
  CONSTRAINT community_assessments_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.locations(id),
  CONSTRAINT community_assessments_conducted_by_cpf_fkey FOREIGN KEY (conducted_by_cpf) REFERENCES public.users(cpf)
);
CREATE TABLE public.community_needs_assessments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  leader_id uuid,
  raw_payload jsonb,
  document_markdown text,
  status text DEFAULT 'novo'::text CHECK (status = ANY (ARRAY['novo'::text, 'em_andamento'::text, 'concluido'::text, 'arquivado'::text])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  conducted_by_cpf character varying,
  organization_id integer,
  location_id integer,
  CONSTRAINT community_needs_assessments_pkey PRIMARY KEY (id),
  CONSTRAINT fk_community_assessments_user FOREIGN KEY (conducted_by_cpf) REFERENCES public.users(cpf),
  CONSTRAINT fk_community_assessments_organization FOREIGN KEY (organization_id) REFERENCES public.organizations(id),
  CONSTRAINT fk_community_assessments_location FOREIGN KEY (location_id) REFERENCES public.locations(id)
);
CREATE TABLE public.community_needs_items (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  assessment_id uuid,
  category text NOT NULL CHECK (category = ANY (ARRAY['agua'::text, 'energia'::text, 'saneamento'::text, 'internet'::text, 'saude'::text, 'educacao'::text, 'seguranca_alimentar'::text, 'transporte'::text, 'acesso'::text, 'ambiental_territorial'::text])),
  priority integer DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
  notes text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT community_needs_items_pkey PRIMARY KEY (id),
  CONSTRAINT community_needs_items_assessment_id_fkey FOREIGN KEY (assessment_id) REFERENCES public.community_needs_assessments(id)
);
CREATE TABLE public.documents (
  id bigint NOT NULL DEFAULT nextval('documents_id_seq'::regclass),
  created_at timestamp with time zone DEFAULT now(),
  content text NOT NULL,
  metadata jsonb DEFAULT '{}'::jsonb,
  document_type text DEFAULT 'general'::text,
  embedding USER-DEFINED,
  owner_user_id_old bigint,
  owner_user_id character varying,
  CONSTRAINT documents_pkey PRIMARY KEY (id),
  CONSTRAINT fk_documents_users FOREIGN KEY (owner_user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.locations (
  id integer NOT NULL DEFAULT nextval('locations_id_seq'::regclass),
  name character varying NOT NULL,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['municipio'::character varying, 'bairro'::character varying, 'comunidade'::character varying, 'favela'::character varying, 'aldeia'::character varying]::text[])),
  parent_location_id integer,
  coordinates jsonb,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp without time zone DEFAULT now(),
  CONSTRAINT locations_pkey PRIMARY KEY (id),
  CONSTRAINT locations_parent_location_id_fkey FOREIGN KEY (parent_location_id) REFERENCES public.locations(id)
);
CREATE TABLE public.organization_locations (
  id integer NOT NULL DEFAULT nextval('organization_locations_id_seq'::regclass),
  organization_id integer,
  location_id integer,
  relationship_type character varying NOT NULL CHECK (relationship_type::text = ANY (ARRAY['sede'::character varying, 'atende'::character varying, 'opera'::character varying, 'representa'::character varying]::text[])),
  active boolean DEFAULT true,
  created_at timestamp without time zone DEFAULT now(),
  CONSTRAINT organization_locations_pkey PRIMARY KEY (id),
  CONSTRAINT organization_locations_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id),
  CONSTRAINT organization_locations_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.locations(id)
);
CREATE TABLE public.organizations (
  id integer NOT NULL DEFAULT nextval('organizations_id_seq'::regclass),
  name character varying NOT NULL,
  type character varying NOT NULL CHECK (type::text = ANY (ARRAY['associacao'::character varying, 'empresa'::character varying, 'aceleradora'::character varying, 'ong'::character varying, 'governo'::character varying]::text[])),
  cnpj character varying,
  primary_location_id integer,
  metadata jsonb DEFAULT '{}'::jsonb,
  active boolean DEFAULT true,
  created_at timestamp without time zone DEFAULT now(),
  CONSTRAINT organizations_pkey PRIMARY KEY (id),
  CONSTRAINT organizations_primary_location_id_fkey FOREIGN KEY (primary_location_id) REFERENCES public.locations(id)
);
CREATE TABLE public.processes (
  id integer NOT NULL DEFAULT nextval('processes_id_seq'::regclass),
  name character varying NOT NULL,
  description text,
  organization_id integer,
  process_type character varying NOT NULL CHECK (process_type::text = ANY (ARRAY['limpeza'::character varying, 'diagnostico'::character varying, 'manutencao'::character varying, 'evento'::character varying, 'reuniao'::character varying, 'inteligencia_comunitaria'::character varying]::text[])),
  automation_config jsonb DEFAULT '{}'::jsonb,
  active boolean DEFAULT true,
  created_at timestamp without time zone DEFAULT now(),
  CONSTRAINT processes_pkey PRIMARY KEY (id),
  CONSTRAINT processes_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id)
);
CREATE TABLE public.tasks (
  id integer NOT NULL DEFAULT nextval('tasks_id_seq'::regclass),
  process_id integer,
  title character varying NOT NULL,
  description text,
  assigned_to_cpf character varying,
  created_by_cpf character varying,
  organization_id integer,
  location_id integer,
  status character varying DEFAULT 'pendente'::character varying CHECK (status::text = ANY (ARRAY['pendente'::character varying, 'em_andamento'::character varying, 'concluida'::character varying, 'cancelada'::character varying]::text[])),
  priority integer DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
  due_date timestamp without time zone,
  completed_at timestamp without time zone,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp without time zone DEFAULT now(),
  CONSTRAINT tasks_pkey PRIMARY KEY (id),
  CONSTRAINT tasks_process_id_fkey FOREIGN KEY (process_id) REFERENCES public.processes(id),
  CONSTRAINT tasks_assigned_to_cpf_fkey FOREIGN KEY (assigned_to_cpf) REFERENCES public.users(cpf),
  CONSTRAINT tasks_created_by_cpf_fkey FOREIGN KEY (created_by_cpf) REFERENCES public.users(cpf),
  CONSTRAINT tasks_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id),
  CONSTRAINT tasks_location_id_fkey FOREIGN KEY (location_id) REFERENCES public.locations(id)
);
CREATE TABLE public.user_contact_methods (
  id bigint NOT NULL DEFAULT nextval('user_contact_methods_id_seq'::regclass),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id_old bigint,
  contact_type text NOT NULL,
  contact_value text NOT NULL,
  is_primary boolean DEFAULT false,
  active boolean DEFAULT true,
  metadata jsonb DEFAULT '{}'::jsonb,
  user_id character varying,
  CONSTRAINT user_contact_methods_pkey PRIMARY KEY (id),
  CONSTRAINT fk_user_contact_methods_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.user_context (
  id bigint NOT NULL DEFAULT nextval('user_context_id_seq'::regclass),
  user_id character varying,
  context_type text NOT NULL CHECK (context_type = ANY (ARRAY['personal'::text, 'community'::text, 'professional'::text])),
  context_data jsonb NOT NULL DEFAULT '{}'::jsonb,
  priority_level integer DEFAULT 5 CHECK (priority_level >= 1 AND priority_level <= 10),
  active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_context_pkey PRIMARY KEY (id),
  CONSTRAINT fk_user_context_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.user_development_matrix (
  id bigint NOT NULL DEFAULT nextval('user_development_matrix_id_seq'::regclass),
  user_id character varying,
  dimension_name text NOT NULL CHECK (dimension_name = ANY (ARRAY['personal'::text, 'social'::text, 'professional'::text, 'community'::text, 'spiritual'::text])),
  current_level integer DEFAULT 1 CHECK (current_level >= 1 AND current_level <= 10),
  progress_data jsonb DEFAULT '{}'::jsonb,
  last_assessment timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_development_matrix_pkey PRIMARY KEY (id),
  CONSTRAINT fk_user_development_matrix_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.user_organization_roles (
  id integer NOT NULL DEFAULT nextval('user_organization_roles_new_id_seq'::regclass),
  user_cpf character varying,
  organization_id integer,
  role_name character varying NOT NULL,
  permissions jsonb DEFAULT '{}'::jsonb,
  active boolean DEFAULT true,
  start_date date DEFAULT CURRENT_DATE,
  end_date date,
  created_at timestamp without time zone DEFAULT now(),
  CONSTRAINT user_organization_roles_pkey PRIMARY KEY (id),
  CONSTRAINT user_organization_roles_new_user_cpf_fkey FOREIGN KEY (user_cpf) REFERENCES public.users(cpf),
  CONSTRAINT user_organization_roles_new_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id)
);
CREATE TABLE public.user_qa_context (
  id bigint NOT NULL DEFAULT nextval('user_qa_context_id_seq'::regclass),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  user_id_old bigint,
  question_category text NOT NULL,
  question text NOT NULL,
  answer text,
  context_type text DEFAULT 'preference'::text CHECK (context_type = ANY (ARRAY['preference'::text, 'requirement'::text, 'constraint'::text, 'goal'::text])),
  priority_level integer DEFAULT 5 CHECK (priority_level >= 1 AND priority_level <= 10),
  tags ARRAY DEFAULT '{}'::text[],
  validation_status text DEFAULT 'pending'::text CHECK (validation_status = ANY (ARRAY['pending'::text, 'confirmed'::text, 'outdated'::text])),
  last_referenced timestamp with time zone,
  reference_count integer DEFAULT 0,
  metadata jsonb DEFAULT '{}'::jsonb,
  active boolean DEFAULT true,
  user_id character varying,
  CONSTRAINT user_qa_context_pkey PRIMARY KEY (id),
  CONSTRAINT fk_user_qa_context_users FOREIGN KEY (user_id) REFERENCES public.users(cpf)
);
CREATE TABLE public.users (
  cpf character varying NOT NULL CHECK (cpf::text ~ '^[0-9]{11}$'::text),
  id bigint NOT NULL DEFAULT nextval('users_id_seq'::regclass) UNIQUE,
  phone character varying NOT NULL CHECK (phone::text ~ '^[0-9+]{10,20}$'::text),
  name character varying NOT NULL,
  email character varying,
  access_level integer NOT NULL DEFAULT 1 CHECK (access_level >= 1 AND access_level <= 7),
  geolocation jsonb NOT NULL DEFAULT '{}'::jsonb,
  onboarding_progress jsonb NOT NULL DEFAULT '{"level": 1, "badges": [], "current_step": "welcome", "completed_steps": [], "gamification_score": 0}'::jsonb,
  active boolean NOT NULL DEFAULT true,
  last_access timestamp with time zone,
  metadata jsonb NOT NULL DEFAULT '{}'::jsonb,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  legacy_id bigint,
  canonical_user_key text,
  contact_methods jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT users_pkey PRIMARY KEY (cpf)
);
