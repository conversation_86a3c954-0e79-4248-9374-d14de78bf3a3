#!/usr/bin/env node

/**
 * Implementar Workflow com Sistema de Memória de Longo Prazo
 * 
 * Este script implementa o novo workflow que inclui:
 * - Information Extractor (IA para extrair entidades)
 * - Switch de Intenções (roteamento baseado em intenções)
 * - <PERSON><PERSON> de Ação (criar tarefas, agendamentos, contexto)
 * 
 * Baseado na discussão com Gemini sobre memória de IA
 */

const fs = require('fs');
const path = require('path');

// Configuração da API n8n
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class MemoryWorkflowImplementer {
  constructor() {
    this.apiUrl = CONFIG.n8nApiUrl;
    this.apiKey = CONFIG.n8nApiKey;
    this.headers = {
      'X-N8N-API-KEY': this.apiKey,
      'Content-Type': 'application/json'
    };
  }

  async makeRequest(endpoint, method = 'GET', data = null) {
    const url = `${this.apiUrl}/api/v1${endpoint}`;
    
    const options = {
      method,
      headers: this.headers
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`❌ Erro na requisição ${method} ${endpoint}:`, error.message);
      throw error;
    }
  }

  async getWorkflows() {
    return await this.makeRequest('/workflows');
  }

  async createWorkflow(workflowData) {
    return await this.makeRequest('/workflows', 'POST', workflowData);
  }

  async updateWorkflow(workflowId, workflowData) {
    return await this.makeRequest(`/workflows/${workflowId}`, 'PUT', workflowData);
  }

  async activateWorkflow(workflowId) {
    return await this.makeRequest(`/workflows/${workflowId}/activate`, 'POST');
  }

  async deactivateWorkflow(workflowId) {
    return await this.makeRequest(`/workflows/${workflowId}/deactivate`, 'POST');
  }

  async loadWorkflowDefinition() {
    const workflowPath = path.join(__dirname, 'unified_workflow_with_memory.json');
    
    if (!fs.existsSync(workflowPath)) {
      throw new Error(`❌ Arquivo não encontrado: ${workflowPath}`);
    }

    const workflowContent = fs.readFileSync(workflowPath, 'utf8');
    return JSON.parse(workflowContent);
  }

  async findExistingWorkflow() {
    const workflows = await this.getWorkflows();
    const workflowList = workflows.data || workflows;
    
    // Buscar workflow atual
    const currentWorkflow = workflowList.find(w => 
      w.name === 'WhatsApp_AI_Assistant_Unified' || 
      w.name === 'WhatsApp_AI_Assistant_Unified_with_Memory'
    );

    return currentWorkflow;
  }

  async implementWorkflow() {
    try {
      console.log('🚀 Iniciando implementação do Sistema de Memória de Longo Prazo...');
      console.log('');

      // 1. Carregar definição do workflow
      console.log('📄 Carregando definição do workflow...');
      const workflowDefinition = await this.loadWorkflowDefinition();
      console.log(`✅ Workflow carregado: ${workflowDefinition.name}`);
      console.log(`📊 Nós: ${workflowDefinition.nodes.length}`);
      console.log('');

      // 2. Verificar workflow existente
      console.log('🔍 Verificando workflow existente...');
      const existingWorkflow = await this.findExistingWorkflow();
      
      if (existingWorkflow) {
        console.log(`📝 Workflow encontrado: ${existingWorkflow.name} (ID: ${existingWorkflow.id})`);
        console.log(`📊 Status: ${existingWorkflow.active ? 'ATIVO' : 'INATIVO'}`);
        
        // Desativar workflow atual se estiver ativo
        if (existingWorkflow.active) {
          console.log('⏸️ Desativando workflow atual...');
          await this.deactivateWorkflow(existingWorkflow.id);
          console.log('✅ Workflow desativado');
        }
        
        // Atualizar workflow existente
        console.log('🔄 Atualizando workflow com sistema de memória...');
        const result = await this.updateWorkflow(existingWorkflow.id, workflowDefinition);
        console.log('✅ Workflow atualizado com sucesso!');
        
        // Ativar workflow atualizado
        console.log('▶️ Ativando workflow atualizado...');
        await this.activateWorkflow(existingWorkflow.id);
        console.log('✅ Workflow ativado!');
        
        return {
          action: 'updated',
          workflowId: existingWorkflow.id,
          workflowName: workflowDefinition.name
        };
        
      } else {
        console.log('🆕 Nenhum workflow existente encontrado. Criando novo...');
        
        // Criar novo workflow
        const result = await this.createWorkflow(workflowDefinition);
        console.log(`✅ Novo workflow criado: ${result.name} (ID: ${result.id})`);
        
        // Ativar novo workflow
        console.log('▶️ Ativando novo workflow...');
        await this.activateWorkflow(result.id);
        console.log('✅ Workflow ativado!');
        
        return {
          action: 'created',
          workflowId: result.id,
          workflowName: result.name
        };
      }

    } catch (error) {
      console.error('❌ Erro durante implementação:', error.message);
      throw error;
    }
  }

  async validateImplementation(workflowId) {
    try {
      console.log('');
      console.log('🔍 Validando implementação...');
      
      const workflows = await this.getWorkflows();
      const workflowList = workflows.data || workflows;
      const workflow = workflowList.find(w => w.id === workflowId);
      
      if (!workflow) {
        throw new Error('Workflow não encontrado após implementação');
      }
      
      console.log(`✅ Workflow validado: ${workflow.name}`);
      console.log(`📊 Status: ${workflow.active ? 'ATIVO' : 'INATIVO'}`);
      console.log(`🔗 Webhook: ${workflow.active ? 'DISPONÍVEL' : 'INDISPONÍVEL'}`);
      
      return workflow;
      
    } catch (error) {
      console.error('❌ Erro na validação:', error.message);
      throw error;
    }
  }

  async run() {
    try {
      console.log('🧠 AI COMTXAE - IMPLEMENTAÇÃO DO SISTEMA DE MEMÓRIA');
      console.log('='.repeat(60));
      console.log('');

      const result = await this.implementWorkflow();
      const validatedWorkflow = await this.validateImplementation(result.workflowId);

      console.log('');
      console.log('🎉 IMPLEMENTAÇÃO CONCLUÍDA COM SUCESSO!');
      console.log('='.repeat(60));
      console.log(`📋 Ação: ${result.action === 'created' ? 'CRIADO' : 'ATUALIZADO'}`);
      console.log(`🆔 ID: ${result.workflowId}`);
      console.log(`📝 Nome: ${result.workflowName}`);
      console.log(`📊 Status: ${validatedWorkflow.active ? 'ATIVO' : 'INATIVO'}`);
      console.log('');
      console.log('🧠 FUNCIONALIDADES IMPLEMENTADAS:');
      console.log('  ✅ Information Extractor (extração de entidades)');
      console.log('  ✅ Switch de Intenções (roteamento inteligente)');
      console.log('  ✅ Criação automática de tarefas');
      console.log('  ✅ Agendamento automático');
      console.log('  ✅ Salvamento de contexto');
      console.log('  ✅ Resposta contextualizada da IA');
      console.log('');
      console.log('🚀 O AI Comtxae agora possui memória de longo prazo!');

    } catch (error) {
      console.error('');
      console.error('💥 FALHA NA IMPLEMENTAÇÃO');
      console.error('='.repeat(60));
      console.error(`❌ Erro: ${error.message}`);
      process.exit(1);
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const implementer = new MemoryWorkflowImplementer();
  implementer.run();
}

module.exports = MemoryWorkflowImplementer;
