# 🤖 AI Comtxae - Sistema de Automação WhatsApp

**Sistema de automação WhatsApp para atendimento de líderes comunitários com IA conversacional e onboarding inteligente gamificado.**

[![Status](https://img.shields.io/badge/Status-MVP_Funcional-green)](https://github.com/lucasblima/AI-Comtxae)
[![Supabase](https://img.shields.io/badge/Supabase-20_Tabelas-green)](https://supabase.com)
[![n8n](https://img.shields.io/badge/n8n-Fluxo_Unificado_Ativo-blue)](https://n8n.io)
[![Evolução](https://img.shields.io/badge/Próximo-Memória_IA-orange)](https://github.com/lucasblima/AI-Comtxae)

## 🎯 Visão Geral

**AI Comtxae** é um sistema completo de automação WhatsApp projetado para atender líderes comunitários (caciques, presidentes de associação, líderes locais) com:

- ✅ **Atendimento 24/7** via WhatsApp com Evolution API
- ✅ **IA Conversacional** (OpenAI) com contexto personalizado
- ✅ **Onboarding Gamificado** com 7 níveis de progressão
- ✅ **Sistema Unificado** baseado em CPF como identificador único
- ✅ **Coleta Estruturada** de necessidades comunitárias
- ✅ **Arquitetura Unificada** com webhook único e 6 subfluxos especializados

## 🏗️ Arquitetura do Sistema

### **Arquitetura Atual (Funcional)**
```
WhatsApp → Extrair/Normalizar → SQL Unificado → IA (GPT-4) → Resposta
```

**Fluxo Implementado:**
```
WhatsApp Webhook →
Extrair e Normalizar →
Processar Usuário e Sessão (SQL Unificado) →
Processar com IA (GPT-4) →
Enviar Resposta →
Webhook Response
```

### **🧠 Próxima Evolução: Sistema de Memória de Longo Prazo**
```
WhatsApp → Information Extractor → Switch (Intenções) → Ações Estruturadas
                                    ├─ Criar Tarefa → tasks
                                    ├─ Agendar → appointments
                                    └─ Contexto → user_context
```

### **Componentes Principais**
- **n8n**: Automação de workflows (Unified_User_Pipeline + 6 subfluxos)
- **Supabase PostgreSQL**: Banco de dados (20 tabelas unificadas baseadas em CPF)
- **Evolution API**: Integração WhatsApp Business
- **OpenAI GPT-4**: IA conversacional contextualizada
- **Context 7 MCP**: Documentação atualizada do n8n v1.111.0
- **LangChain**: Memória e contexto de conversação

## 📊 Status Atual do Sistema

### ✅ **Sistema Operacional**
- **Supabase**: `assistente_v0.1` - **ACTIVE_HEALTHY** (sa-east-1)
- **PostgreSQL**: v17.4.1.048
- **Tabelas**: 20 tabelas unificadas com CPF como PK
- **Performance**: Índices otimizados para queries CPF-based
- **Workflows**: Unified_User_Pipeline + 6 subfluxos especializados

### 🎯 **Métricas Atuais**
- **Usuários**: Sistema unificado baseado em CPF
- **Sessões**: 48+ sessões de chat migradas
- **Mensagens**: 110+ mensagens processadas
- **Documentos**: Base RAG integrada ao sistema
- **Uptime**: 99.5%+ (Supabase ACTIVE_HEALTHY)

## 🚀 Início Rápido

### **Para Desenvolvedores**
1. **Clone o repositório**:
   ```bash
   git clone https://github.com/lucasblima/AI-Comtxae.git
   cd AI-Comtxae
   ```

2. **Configure as variáveis de ambiente**:
   ```bash
   cp config/environments/.env.example .env
   # Configure suas chaves de API
   ```

3. **Consulte a documentação**:
   - 📋 **Estado Atual**: [`docs/analysis/ANALISE_ESTADO_ATUAL_MVP.md`](docs/analysis/ANALISE_ESTADO_ATUAL_MVP.md)
   - 🏗️ **Arquitetura**: [`docs/architecture/ARCHITECTURE_VALIDATION.md`](docs/architecture/ARCHITECTURE_VALIDATION.md)
   - 📚 **Documentação Completa**: [`docs/README.md`](docs/README.md)

### **Para Remote Agents**
- 🎯 **Prompts Executivos**: [`README_PROJETO_LEAN.md`](README_PROJETO_LEAN.md)
- 📋 **Checklist de Validação**: [`docs/CHECKLIST_VALIDACAO_REMOTE_AGENTS.md`](docs/CHECKLIST_VALIDACAO_REMOTE_AGENTS.md)
- 🔧 **Troubleshooting**: [`docs/TROUBLESHOOTING_GUIDE.md`](docs/TROUBLESHOOTING_GUIDE.md)

## 📁 Estrutura do Projeto

```
AI-Comtxae/
├── 📚 docs/                          # Documentação completa
│   ├── analysis/                     # Análises do sistema
│   ├── architecture/                 # Documentação arquitetural
│   ├── planning/                     # Planejamento e requisitos
│   └── protocols/                    # Protocolos de trabalho
├── 🔧 automation/                     # Scripts de automação
├── ⚙️ config/                        # Configurações
├── 🔄 workflows/                     # Workflows n8n (backup)
└── 📋 README_PROJETO_LEAN.md         # Prompts para Remote Agents
```

## 🎯 Casos de Uso

### **Para Líderes Comunitários**
- 💬 **Conversa Natural**: Interação via WhatsApp com IA
- 📝 **Onboarding Gamificado**: 7 níveis de progressão
- 🏘️ **Coleta de Necessidades**: Água, energia, saúde, educação
- 📊 **Relatórios Automáticos**: Documentos gerados automaticamente

### **Para Organizações**
- 📈 **Dados Estruturados**: Necessidades categorizadas
- 🎯 **Segmentação Inteligente**: Por região, tipo, prioridade
- 📋 **Relatórios Executivos**: Dashboards e métricas
- 🔄 **Automação Completa**: Fluxo end-to-end automatizado

## 🛠️ Tecnologias Utilizadas

| Componente | Tecnologia | Status | Função |
|------------|------------|--------|---------|
| **Workflows** | n8n | ✅ Ativo | Automação e orquestração |
| **Banco de Dados** | Supabase PostgreSQL | ✅ Ativo | Persistência e RAG |
| **WhatsApp** | Evolution API | ✅ Ativo | Integração de mensagens |
| **IA** | OpenAI GPT-4 | ✅ Ativo | Processamento conversacional |
| **Memória** | LangChain | ✅ Ativo | Contexto e histórico |

## 📞 Suporte e Contribuição

### **Documentação**
- 📚 **Documentação Completa**: [`docs/`](docs/)
- 🔍 **Análise do Sistema**: [`docs/analysis/ANALISE_ESTADO_ATUAL_MVP.md`](docs/analysis/ANALISE_ESTADO_ATUAL_MVP.md)
- 🏗️ **Validação Arquitetural**: [`docs/architecture/ARCHITECTURE_VALIDATION.md`](docs/architecture/ARCHITECTURE_VALIDATION.md)

### **Para Desenvolvedores**
- 🔧 **Protocolos**: [`docs/protocols/PROTOCOLO_EDICAO_WORKFLOWS.md`](docs/protocols/PROTOCOLO_EDICAO_WORKFLOWS.md)
- 📋 **Planejamento**: [`docs/planning/PLANO_EXECUTIVO_MVP.md`](docs/planning/PLANO_EXECUTIVO_MVP.md)

### **Contato**
- 👤 **Maintainer**: Lucas Boscacci Lima (@lucasblima)
- 📧 **Email**: <EMAIL>
- 🔗 **GitHub**: [github.com/lucasblima/AI-Comtxae](https://github.com/lucasblima/AI-Comtxae)

## 📄 Licença

Este projeto está sob licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

---

**🎯 Sistema pronto para produção | ✅ Documentação atualizada em 2025-09-18**
