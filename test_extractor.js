#!/usr/bin/env node

/**
 * Teste do Information Extractor
 * 
 * Script simples para testar a funcionalidade de extração de entidades
 */

const fs = require('fs');

// Configuração da API n8n
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class ExtractorTester {
  constructor() {
    this.apiUrl = CONFIG.n8nApiUrl;
    this.apiKey = CONFIG.n8nApiKey;
    this.headers = {
      'X-N8N-API-KEY': this.apiKey,
      'Content-Type': 'application/json'
    };
  }

  async makeRequest(endpoint, method = 'GET', data = null) {
    const url = `${this.apiUrl}/api/v1${endpoint}`;
    
    const options = {
      method,
      headers: this.headers
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`❌ Erro na requisição ${method} ${endpoint}:`, error.message);
      throw error;
    }
  }

  async createTestWorkflow() {
    const workflowPath = './test_information_extractor.json';
    const workflowContent = fs.readFileSync(workflowPath, 'utf8');
    const workflowData = JSON.parse(workflowContent);

    console.log('🧪 Criando workflow de teste...');
    const result = await this.makeRequest('/workflows', 'POST', workflowData);
    console.log(`✅ Workflow criado: ${result.name} (ID: ${result.id})`);

    return result;
  }

  async activateWorkflow(workflowId) {
    console.log('▶️ Ativando workflow...');
    await this.makeRequest(`/workflows/${workflowId}/activate`, 'POST');
    console.log('✅ Workflow ativado!');
  }

  async testExtraction(workflowId) {
    console.log('');
    console.log('🧠 Testando extração de entidades...');
    
    const testMessages = [
      'Lembre-me de ligar para João amanhã',
      'Marcar reunião com Maria sobre o projeto na terça-feira',
      'Como está o tempo hoje?',
      'Preciso comprar leite no mercado',
      'Agendar consulta médica para próxima semana'
    ];

    for (const message of testMessages) {
      console.log(`\n📝 Testando: "${message}"`);
      
      try {
        // Simular chamada do webhook (isso seria feito via HTTP em produção)
        console.log('   ⏳ Processando...');
        
        // Em um teste real, você faria uma requisição HTTP para o webhook
        // Por enquanto, apenas mostramos que o workflow foi criado
        console.log('   ✅ Workflow pronto para receber esta mensagem');
        
      } catch (error) {
        console.log(`   ❌ Erro: ${error.message}`);
      }
    }
  }

  async cleanup(workflowId) {
    console.log('');
    console.log('🧹 Limpando workflow de teste...');
    
    try {
      // Desativar workflow
      await this.makeRequest(`/workflows/${workflowId}/deactivate`, 'POST');
      console.log('⏸️ Workflow desativado');
      
      // Deletar workflow
      await this.makeRequest(`/workflows/${workflowId}`, 'DELETE');
      console.log('🗑️ Workflow removido');
      
    } catch (error) {
      console.log(`⚠️ Erro na limpeza: ${error.message}`);
    }
  }

  async run() {
    try {
      console.log('🧪 TESTE DO INFORMATION EXTRACTOR');
      console.log('='.repeat(50));
      console.log('');

      // Criar workflow de teste
      const workflow = await this.createTestWorkflow();
      
      // Ativar workflow
      await this.activateWorkflow(workflow.id);
      
      // Testar extração
      await this.testExtraction(workflow.id);
      
      console.log('');
      console.log('🎉 TESTE CONCLUÍDO!');
      console.log('='.repeat(50));
      console.log(`🆔 Workflow ID: ${workflow.id}`);
      console.log('📋 O workflow está ativo e pronto para testes manuais');
      console.log('');
      console.log('💡 Para testar manualmente:');
      console.log(`   1. Acesse: ${this.apiUrl}/webhook/test-extractor`);
      console.log('   2. Envie POST com: {"message": "sua mensagem aqui"}');
      console.log('   3. Veja a resposta com dados extraídos');
      console.log('');
      console.log('⚠️ Lembre-se de remover o workflow após os testes!');

    } catch (error) {
      console.error('');
      console.error('💥 FALHA NO TESTE');
      console.error('='.repeat(50));
      console.error(`❌ Erro: ${error.message}`);
      process.exit(1);
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const tester = new ExtractorTester();
  tester.run();
}

module.exports = ExtractorTester;
