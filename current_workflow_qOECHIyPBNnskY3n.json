{"createdAt": "2025-09-19T18:13:50.632Z", "updatedAt": "2025-09-24T19:37:37.000Z", "id": "qOECHIyPBNnskY3n", "name": "Unified_User_Pipeline", "active": true, "isArchived": false, "nodes": [{"parameters": {"jsCode": "// Pega os dados de DENTRO do 'body' do webhook.\nconst webhookData = $input.all()[0].json.body;\n\n// O resto do código permanece igual e agora vai funcionar.\nconst data = webhookData.data || {};\nconst messageData = data.message || {};\n\n// 1. Lógica de extração do remetente (mais robusta)\nconst from = data.key?.remoteJid || webhookData.sender || webhookData.from;\n\n// 2. Validação para evitar o erro\nif (!from) {\n  console.log('⚠️ Número do remetente (from) não encontrado. Ignorando evento.');\n  return null; \n}\n\n// 3. Normalização do telefone\nconst phone = from.replace(/[^0-9]/g, '');\n\n// 4. Extração do conteúdo da mensagem (texto, áudio, imagem, etc.)\nlet message = '';\nlet messageType = 'text';\nlet mediaUrl = null;\n\nif (messageData.conversation) {\n  message = messageData.conversation;\n  messageType = 'text';\n} else if (messageData.extendedTextMessage?.text) {\n  message = messageData.extendedTextMessage.text;\n  messageType = 'text';\n} else if (messageData.imageMessage) {\n  message = messageData.imageMessage.caption || '[Imagem recebida]';\n  messageType = 'image';\n} else if (messageData.audioMessage) {\n  message = '[Áudio recebido]';\n  messageType = 'audio';\n} else if (messageData.videoMessage) {\n  message = messageData.videoMessage.caption || '[Vídeo recebido]';\n  messageType = 'video';\n}\n\n// 5. Geração e montagem dos dados finalizados\nconst cpf = phone.length >= 11 ? phone.slice(-11) : phone.padStart(11, '0');\nconst pushName = data.pushName || webhookData.pushName || 'Usuário';\n\nconst normalizedData = {\n  cpf: cpf,\n  phone: phone,\n  pushName: pushName,\n  message: message.trim(),\n  messageType: messageType,\n  mediaUrl: mediaUrl,\n  timestamp: new Date().toISOString(),\n  source: 'whatsapp',\n  raw_data: webhookData\n};\n\nconsole.log('📱 Dados normalizados:', normalizedData);\n\nreturn [{ json: normalizedData }];"}, "id": "797d3f79-4c8d-46ff-a204-26a76f0f05d4", "name": "Extrair e Normalizar", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2400, 1072]}, {"parameters": {"respondWith": "allIncomingItems", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.4, "position": [4384, 624], "id": "c2e2826d-d81a-48b3-9d79-4548ad385d14", "name": "Respond to Webhook"}, {"parameters": {"options": {"temperature": 0}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [3696, 736], "id": "9d19261b-a225-48af-930d-050f84075eb2", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "ysF0fmOEP6Ej61UC", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"httpMethod": "POST", "path": "whatsapp-webhook", "options": {}}, "id": "59ba1aeb-71a8-4097-9d2c-41710c339652", "name": "WhatsApp Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [2176, 1072], "webhookId": "whatsapp-unified-webhook"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "typeVersion": 1.5, "position": [3824, 736], "id": "fc67eddd-b6b1-4a8d-97a3-946a2e5a2037", "name": "Redis <PERSON>", "credentials": {"redis": {"id": "3IHTtSKb6N7gBIjz", "name": "Redis account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "-- Insere um novo usuário se o telefone não existir.\n-- Se existir, apenas atualiza a data do último acesso.\nINSERT INTO users (cpf, phone, name, last_access)\nVALUES ($1, $2, $3, NOW())\nON CONFLICT (phone) \nDO UPDATE SET \n  last_access = NOW(),\n  name = COALESCE(users.name, EXCLUDED.name); -- Atualiza o nome apenas se estiver vazio\n\n-- <PERSON><PERSON><PERSON>, seleciona os dados do usuário para usar nos próximos nós.\nSELECT cpf, name, phone, access_level, onboarding_progress\nFROM users \nWHERE phone = $2;\n", "options": {"queryReplacement": "={{ $json.cpf }},{{ $json.phone }},{{ $json.pushName }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [3472, 1056], "id": "d3b874a9-e013-4ce8-afd3-0d9ef7e638b2", "name": "Buscar/Criar <PERSON>", "credentials": {"postgres": {"id": "tqtLhtoqDEUD7G1k", "name": "assistente_v0.1"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT MAX(id) as session_id\nFROM chat_sessions\nWHERE user_id = $1\n  AND active = true\n  AND last_activity > NOW() - INTERVAL '5 hours';", "options": {"queryReplacement": "={{ $json.cpf }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [3696, 1056], "id": "21ec095e-cf3e-4550-b436-a8d2b859dac2", "name": "Gerenciar Sessão", "alwaysOutputData": false, "credentials": {"postgres": {"id": "tqtLhtoqDEUD7G1k", "name": "assistente_v0.1"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "82829e5a-1c08-46f8-892e-1522272d1585", "leftValue": "={{ $json.session_id?.toNumber() }}", "rightValue": 0, "operator": {"type": "number", "operation": "notEmpty", "singleValue": true}}], "combinator": "or"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3920, 1056], "id": "473e6a57-f1a5-4f5e-b62c-2400fde6942c", "name": "If", "alwaysOutputData": false}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO chat_sessions \n    (user_id, channel_identifier, active)\nVALUES \n    ($1, $2, true)\nRETURNING id as session_id; -- RETURNING para já termos o ID da nova sessão", "options": {"queryReplacement": "={{ $('Buscar/Criar Usuário').item.json.cpf }},{{ $('Buscar/Criar Usuário').item.json.phone }}"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [4144, 1136], "id": "61d1d0d7-f63a-4363-a650-ee32a9caa9a5", "name": "<PERSON><PERSON><PERSON>", "credentials": {"postgres": {"id": "tqtLhtoqDEUD7G1k", "name": "assistente_v0.1"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO chat_messages\n    (session_id, user_id, content, message_type)\nVALUES\n    ($1, $2, $3, 'text');", "options": {"queryReplacement": "={{ $('Gerenciar Sessão').first().json.session_id || $('Criar Sessão').first().json.session_id }},{{ $('Buscar/Criar Usuário').first().json.cpf }},{{ $('Extrair e Normalizar').first().json.message }}\n"}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [4368, 1056], "id": "f79069ca-ad08-4051-b871-8b6b7abf1f11", "name": "<PERSON><PERSON>", "credentials": {"postgres": {"id": "tqtLhtoqDEUD7G1k", "name": "assistente_v0.1"}}}, {"parameters": {"calendar": {"__rl": true, "value": "<EMAIL>", "mode": "list", "cachedResultName": "<EMAIL>"}, "additionalFields": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [3952, 736], "id": "44f7b937-15c6-4810-8916-61ebf160519c", "name": "Create an event in Google Calendar", "credentials": {"googleCalendarOAuth2Api": {"id": "PfBG4ZiV8Z7xciYS", "name": "Comtxae Google Calendar"}}}, {"parameters": {"promptType": "define", "text": "Assistente \"AI Comtxae\" para desenvolvimento comunitário.", "options": {"systemMessage": "You are a helpful assistant", "maxIterations": 10, "returnIntermediateSteps": false, "passthroughBinaryImages": true, "batching": {"batchSize": 1, "delayBetweenBatches": 0}, "enableStreaming": true}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [3760, 512], "id": "94ba0b43-7d50-45f6-8305-50e05fe994e3", "name": "Scheduler AI Agent"}, {"parameters": {"modelId": {"__rl": true, "value": "models/gemini-1.5-flash-latest", "mode": "list", "cachedResultName": "models/gemini-1.5-flash-latest"}, "messages": {"values": [{"content": "=Analise a mensagem do usuário e classifique a intenção principal. Responda APENAS com uma das seguintes palavras: 'agendamento' ou 'conversa_geral'.\n\nMensagem do usuário: \"{{ $('Extrair e Normalizar').first().json.message }}\"\n", "role": "model"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.googleGemini", "typeVersion": 1, "position": [3344, 624], "id": "aa22a2b6-91ad-4c80-849f-3bfe1f6e3aac", "name": "Message a model", "credentials": {"googlePalmApi": {"id": "ysF0fmOEP6Ej61UC", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.messageType }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals"}, "id": "8621db14-9eda-4fe9-bc09-b6c1600d571f"}], "combinator": "and"}, "renameOutput": true, "outputKey": "text"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "91edf096-e2e1-4aee-a99b-5edfc85ad693", "leftValue": "={{ $json.messageType }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "audio"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "48d947b1-767e-4a68-9de3-ad29ce45f47e", "leftValue": "={{ $json.messageType }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "image"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "6dc38136-f2ad-4463-91bb-d9b0c116ca9e", "leftValue": "={{ $json.messageType }}", "rightValue": "video", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "video"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "9f5f7c98-4608-4650-8e0a-8ac490bf0e50", "leftValue": "={{ $json.messageType }}", "rightValue": "document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "documento"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [2624, 1024], "id": "7abcdcfc-69c4-4b24-8302-9a74f078134f", "name": "Switch", "alwaysOutputData": false}, {"parameters": {"resource": "messages-api", "instanceName": "Lucas_4569", "options_message": {}}, "type": "n8n-nodes-evolution-api.evolutionApi", "typeVersion": 1, "position": [4160, 624], "id": "ad19273f-3709-4334-8fa1-4e8f911f8b46", "name": "Enviar texto", "credentials": {"evolutionApi": {"id": "kAtQaO78sD6y7HoX", "name": "Evolution account"}}, "disabled": true}], "connections": {"Extrair e Normalizar": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "Scheduler AI Agent", "type": "ai_languageModel", "index": 0}]]}, "WhatsApp Webhook": {"main": [[{"node": "Extrair e Normalizar", "type": "main", "index": 0}]]}, "Redis Chat Memory": {"ai_memory": [[{"node": "Scheduler AI Agent", "type": "ai_memory", "index": 0}]]}, "Buscar/Criar Usuário": {"main": [[{"node": "Gerenciar Sessão", "type": "main", "index": 0}]]}, "Gerenciar Sessão": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Criar Sessão": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Salvar Mensagem": {"main": [[]]}, "Create an event in Google Calendar": {"ai_tool": [[{"node": "Scheduler AI Agent", "type": "ai_tool", "index": 0}]]}, "Scheduler AI Agent": {"main": [[{"node": "Enviar texto", "type": "main", "index": 0}]]}, "Message a model": {"main": [[{"node": "Scheduler AI Agent", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Buscar/Criar <PERSON>", "type": "main", "index": 0}], []]}, "Enviar texto": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "timezone": "America/Sao_Paulo", "callerPolicy": "workflowsFromSameOwner"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "67320333-097e-4862-8b45-2c398e3689c6", "triggerCount": 1, "shared": [{"createdAt": "2025-09-19T18:13:50.639Z", "updatedAt": "2025-09-19T18:13:50.639Z", "role": "workflow:owner", "workflowId": "qOECHIyPBNnskY3n", "projectId": "w2JnJzxqaBmp0wHI", "project": {"createdAt": "2025-06-21T02:06:51.723Z", "updatedAt": "2025-06-21T02:08:09.808Z", "id": "w2JnJzxqaBmp0wHI", "name": "<PERSON> <l<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "type": "personal", "icon": null, "description": null, "projectRelations": [{"createdAt": "2025-06-21T02:06:51.723Z", "updatedAt": "2025-06-21T02:06:51.723Z", "userId": "96b002a8-2886-4136-81f9-4bf3c203eec5", "projectId": "w2JnJzxqaBmp0wHI", "user": {"createdAt": "2025-06-21T02:06:50.902Z", "updatedAt": "2025-09-24T14:18:02.000Z", "id": "96b002a8-2886-4136-81f9-4bf3c203eec5", "email": "l<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "personalizationAnswers": {"version": "v4", "personalization_survey_submitted_at": "2025-06-21T02:09:05.086Z", "personalization_survey_n8n_version": "1.79.3", "companyIndustryExtended": ["msp"], "companySize": "1000+", "companyType": "other", "role": "business-owner", "reportedSource": "youtube"}, "settings": {"userActivated": true, "easyAIWorkflowOnboarded": true, "firstSuccessfulWorkflowId": "GLGwwaUo1I6btYdo", "userActivatedAt": 1751483434942, "npsSurvey": {"responded": true, "lastShownAt": 1753198478411}}, "disabled": false, "mfaEnabled": false, "lastActiveAt": "2025-09-24", "isPending": false}}]}}], "tags": [{"createdAt": "2025-07-16T01:34:40.547Z", "updatedAt": "2025-07-16T01:34:40.547Z", "id": "mwVPLlxntLCIITnt", "name": "AI Comtxae"}]}