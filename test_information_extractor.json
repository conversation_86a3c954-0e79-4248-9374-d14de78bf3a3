{"name": "Test_Information_Extractor", "active": false, "nodes": [{"parameters": {"httpMethod": "POST", "path": "test-extractor", "options": {}}, "id": "test-webhook", "name": "Test Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [100, 300]}, {"parameters": {"resource": "chat", "operation": "create", "model": "gpt-4", "messages": {"values": [{"role": "system", "content": "Você é um assistente de IA especializado em extrair informações estruturadas de mensagens.\n\nSua tarefa é analisar a mensagem do usuário e extrair:\n1. INTENÇÃO principal (criacao_tarefa, agendamento, conversa_geral)\n2. DETALHES específicos baseados na intenção\n\nResponda APENAS com um JSON válido seguindo este schema:\n\n```json\n{\n  \"intent\": \"criacao_tarefa|agendamento|conversa_geral\",\n  \"task_description\": \"string ou null\",\n  \"due_date\": \"string ou null\",\n  \"appointment_subject\": \"string ou null\",\n  \"appointment_person\": \"string ou null\",\n  \"context_info\": \"string ou null\",\n  \"confidence\": 0.0-1.0\n}\n```\n\nExemplos:\n- \"Lembre-me de ligar para João amanhã\" → {\"intent\": \"criacao_tarefa\", \"task_description\": \"ligar para <PERSON>\", \"due_date\": \"amanhã\"}\n- \"Marcar reunião com Maria sobre projeto\" → {\"intent\": \"agendamento\", \"appointment_subject\": \"projeto\", \"appointment_person\": \"Maria\"}\n- \"Como está o tempo hoje?\" → {\"intent\": \"conversa_geral\", \"context_info\": \"pergunta sobre clima\"}"}, {"role": "user", "content": "={{ $json.message || '<PERSON><PERSON><PERSON>, como você está?' }}"}]}, "options": {"temperature": 0.1, "maxTokens": 200}}, "id": "information-extractor", "name": "Information Extractor", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [300, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"status\": \"success\",\n  \"message\": \"Extração realizada com sucesso\",\n  \"input_message\": \"{{ $json.message }}\",\n  \"extracted_data\": \"{{ $('Information Extractor').item.json.choices[0].message.content }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "webhook-response", "name": "Webhook Response", "type": "n8n-nodes-base.webhookResponse", "typeVersion": 1, "position": [500, 300]}], "connections": {"Test Webhook": {"main": [[{"node": "Information Extractor", "type": "main", "index": 0}]]}, "Information Extractor": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "meta": {"templateCredsSetupCompleted": true}}