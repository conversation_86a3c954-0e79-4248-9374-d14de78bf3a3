#!/usr/bin/env node

/**
 * Obter Workflow Atual
 * 
 * Script para baixar o workflow atual do n8n e analisar sua estrutura
 */

const fs = require('fs');

// Configuração da API n8n
const CONFIG = {
  n8nApiUrl: process.env.N8N_API_URL || 'https://n8n-n8n.w9jo16.easypanel.host',
  n8nApiKey: process.env.N8N_API_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI5NmIwMDJhOC0yODg2LTQxMzYtODFmOS00YmYzYzIwM2VlYzUiLCJpc3MiOiJuOG4iLCJhdWQiOiJwdWJsaWMtYXBpIiwiaWF0IjoxNzU2MjIxMjExLCJleHAiOjE3NTg3NzI4MDB9.6BzCKbfcBOMtPukrVHFN1Mq2B4Fe8QezboF7CRp323M'
};

class WorkflowAnalyzer {
  constructor() {
    this.apiUrl = CONFIG.n8nApiUrl;
    this.apiKey = CONFIG.n8nApiKey;
    this.headers = {
      'X-N8N-API-KEY': this.apiKey,
      'Content-Type': 'application/json'
    };
  }

  async makeRequest(endpoint, method = 'GET', data = null) {
    const url = `${this.apiUrl}/api/v1${endpoint}`;
    
    const options = {
      method,
      headers: this.headers
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`❌ Erro na requisição ${method} ${endpoint}:`, error.message);
      throw error;
    }
  }

  async getCurrentWorkflow() {
    console.log('🔍 Buscando workflow atual...');
    
    // ID do workflow ativo encontrado
    const workflowId = 'qOECHIyPBNnskY3n';
    
    const workflow = await this.makeRequest(`/workflows/${workflowId}`);
    console.log(`✅ Workflow obtido: ${workflow.name}`);
    console.log(`📊 Nós: ${workflow.nodes.length}`);
    console.log(`📊 Status: ${workflow.active ? 'ATIVO' : 'INATIVO'}`);
    
    return workflow;
  }

  analyzeWorkflow(workflow) {
    console.log('');
    console.log('🔍 ANÁLISE DO WORKFLOW ATUAL');
    console.log('='.repeat(50));
    
    console.log(`📝 Nome: ${workflow.name}`);
    console.log(`🆔 ID: ${workflow.id}`);
    console.log(`📊 Status: ${workflow.active ? 'ATIVO' : 'INATIVO'}`);
    console.log(`🔢 Total de nós: ${workflow.nodes.length}`);
    console.log('');
    
    console.log('📋 ESTRUTURA DOS NÓS:');
    workflow.nodes.forEach((node, index) => {
      console.log(`   ${index + 1}. ${node.name} (${node.type})`);
    });
    
    console.log('');
    console.log('🔗 CONEXÕES:');
    Object.keys(workflow.connections).forEach(nodeFrom => {
      const connections = workflow.connections[nodeFrom];
      if (connections.main && connections.main[0]) {
        connections.main[0].forEach(conn => {
          console.log(`   ${nodeFrom} → ${conn.node}`);
        });
      }
    });
    
    // Verificar se já tem componentes de IA
    const aiNodes = workflow.nodes.filter(node => 
      node.type.includes('langchain') || 
      node.type.includes('openAi') ||
      node.type.includes('googleGemini') ||
      node.name.toLowerCase().includes('ai') ||
      node.name.toLowerCase().includes('agent')
    );
    
    console.log('');
    console.log('🧠 COMPONENTES DE IA EXISTENTES:');
    if (aiNodes.length > 0) {
      aiNodes.forEach(node => {
        console.log(`   ✅ ${node.name} (${node.type})`);
      });
    } else {
      console.log('   ❌ Nenhum componente de IA encontrado');
    }
    
    return {
      hasAI: aiNodes.length > 0,
      aiNodes: aiNodes,
      totalNodes: workflow.nodes.length
    };
  }

  async saveWorkflow(workflow) {
    const filename = `current_workflow_${workflow.id}.json`;
    try {
      fs.writeFileSync(filename, JSON.stringify(workflow, null, 2), 'utf8');
      console.log('');
      console.log(`💾 Workflow salvo em: ${filename}`);
      console.log(`📊 Tamanho do arquivo: ${fs.statSync(filename).size} bytes`);
      return filename;
    } catch (error) {
      console.error(`❌ Erro ao salvar arquivo: ${error.message}`);
      throw error;
    }
  }

  async run() {
    try {
      console.log('📊 ANÁLISE DO WORKFLOW ATUAL - AI COMTXAE');
      console.log('='.repeat(60));
      console.log('');

      const workflow = await this.getCurrentWorkflow();
      const analysis = this.analyzeWorkflow(workflow);
      const filename = await this.saveWorkflow(workflow);

      console.log('');
      console.log('🎯 PRÓXIMOS PASSOS RECOMENDADOS:');
      console.log('='.repeat(50));
      
      if (analysis.hasAI) {
        console.log('✅ O workflow já possui componentes de IA!');
        console.log('💡 Recomendação: Evoluir o workflow existente');
        console.log('   - Adicionar Information Extractor após processamento atual');
        console.log('   - Implementar Switch de intenções');
        console.log('   - Adicionar nós de ação para estruturação de dados');
      } else {
        console.log('⚠️ O workflow não possui componentes de IA');
        console.log('💡 Recomendação: Adicionar sistema de memória completo');
      }
      
      console.log('');
      console.log(`📁 Arquivo salvo: ${filename}`);
      console.log('🔧 Use este arquivo como base para modificações');

    } catch (error) {
      console.error('');
      console.error('💥 FALHA NA ANÁLISE');
      console.error('='.repeat(50));
      console.error(`❌ Erro: ${error.message}`);
      process.exit(1);
    }
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  const analyzer = new WorkflowAnalyzer();
  analyzer.run();
}

module.exports = WorkflowAnalyzer;
